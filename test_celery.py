#!/usr/bin/env python3
"""
Script để test Celery worker và tasks
"""

import sys
import os
import asyncio
import logging

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_celery_connection():
    """Test Celery connection và configuration"""
    try:
        from app.core.celery_app import celery_app
        
        logger.info("🔧 Testing Celery configuration...")
        logger.info(f"   Broker URL: {celery_app.conf.broker_url}")
        logger.info(f"   Result Backend: {celery_app.conf.result_backend}")
        logger.info(f"   Task Routes: {celery_app.conf.task_routes}")
        
        # Test basic connection
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            logger.info("✅ Celery workers found:")
            for worker, stat in stats.items():
                logger.info(f"   Worker: {worker}")
        else:
            logger.warning("⚠️ No Celery workers found!")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Celery connection test failed: {e}")
        return False

def test_simple_task():
    """Test simple Celery task"""
    try:
        from app.tasks.slide_generation_tasks import test_slide_generation_task
        
        logger.info("🧪 Testing simple Celery task...")
        
        result = test_slide_generation_task.delay("Test message from script")
        logger.info(f"   Task ID: {result.id}")
        logger.info(f"   Task State: {result.state}")
        
        # Wait for result (with timeout)
        try:
            task_result = result.get(timeout=10)
            logger.info(f"✅ Task completed: {task_result}")
            return True
        except Exception as e:
            logger.error(f"❌ Task failed or timeout: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Simple task test failed: {e}")
        return False

def test_slide_generation_task():
    """Test slide generation task trigger"""
    try:
        from app.tasks.slide_generation_tasks import trigger_slide_generation_task
        
        logger.info("🎯 Testing slide generation task trigger...")
        
        async def run_test():
            task_id = await trigger_slide_generation_task(
                lesson_id="test_lesson",
                template_id="test_template",
                config_prompt="test config",
                presentation_title="Test Presentation"
            )
            logger.info(f"✅ Slide generation task triggered: {task_id}")
            return task_id
        
        task_id = asyncio.run(run_test())
        return task_id
        
    except Exception as e:
        logger.error(f"❌ Slide generation task test failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting Celery tests...")
    
    # Test 1: Celery connection
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Celery Connection")
    logger.info("="*50)
    connection_ok = test_celery_connection()
    
    # Test 2: Simple task
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Simple Task")
    logger.info("="*50)
    simple_task_ok = test_simple_task()
    
    # Test 3: Slide generation task
    logger.info("\n" + "="*50)
    logger.info("TEST 3: Slide Generation Task")
    logger.info("="*50)
    slide_task_ok = test_slide_generation_task()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    logger.info(f"Celery Connection: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    logger.info(f"Simple Task: {'✅ PASS' if simple_task_ok else '❌ FAIL'}")
    logger.info(f"Slide Generation Task: {'✅ PASS' if slide_task_ok else '❌ FAIL'}")
    
    if not connection_ok:
        logger.error("\n❌ Celery worker is not running!")
        logger.error("   Start worker with: celery -A app.core.celery_app worker --loglevel=info --pool=solo")
    
    if not simple_task_ok and connection_ok:
        logger.error("\n❌ Task execution failed!")
        logger.error("   Check Redis connection and worker logs")
